import axios from 'axios';
import dotenv from 'dotenv';
import { logger } from './logger.js'; // Import logger from the new module
import { fetchNewsApiArticles } from './newsapi_fallback.js'; // Import the fallback function

dotenv.config();

const GNEWS_API_KEY = process.env.GNEWS_API_KEY;

if (!GNEWS_API_KEY) {
  logger.error("Error: Missing required GNews API key environment variable."); // Use logger
  process.exit(1);
}

const MAX_QUERY_LENGTH = 450; // Define a max length for GNews queries

const fetchTrendingArticles = async (topic, keywords) => {
  let allArticles = [];
  const seenUrls = new Set();

  // Helper function to clean and truncate query safely
  const cleanAndTruncateQuery = (query) => {
    // Clean the query: remove apostrophes and potentially other problematic chars
    let cleanedQuery = query.replace(/'/g, ''); // Remove apostrophes

    if (cleanedQuery.length > MAX_QUERY_LENGTH) {
      let truncated = cleanedQuery.substring(0, MAX_QUERY_LENGTH);
      // Ensure we don't cut in the middle of a word
      const lastSpaceIndex = truncated.lastIndexOf(' ');
      if (lastSpaceIndex > 0) {
        truncated = truncated.substring(0, lastSpaceIndex);
      }
      logger.warn(`Query truncated due to length limit. Original length: ${cleanedQuery.length}, Truncated length: ${truncated.length}`);
      return truncated.trim();
    }
    return cleanedQuery.trim(); // Return the cleaned (and potentially truncated) query
  };

  // First try fetching a specific article with both topic and keywords
  let specificQuery = `"${topic}" "${keywords.join(" ")}"`.trim();
  specificQuery = cleanAndTruncateQuery(specificQuery); // Clean and truncate
  logger.info(`Attempting GNews fetch with specific query: ${specificQuery}`); // Use logger
  const specificArticles = await fetchArticles(specificQuery, seenUrls);
  logger.info(`[gnews.js] Fetched ${specificArticles.length} specific articles.`); // Use logger
  allArticles.push(...specificArticles); // Add specific articles, duplicates handled by seenUrls

  // Also fetch related articles using the combined query
  let combinedQuery = `${topic} ${keywords.join(" ")}`.trim();
  combinedQuery = cleanAndTruncateQuery(combinedQuery); // Clean and truncate
  logger.info(`Attempting GNews fetch with combined query for related articles: ${combinedQuery}`); // Use logger
  const relatedArticles = await fetchArticles(combinedQuery, seenUrls);
  logger.info(`[gnews.js] Fetched ${relatedArticles.length} related articles.`); // Use logger
  allArticles.push(...relatedArticles); // Add related articles, duplicates handled by seenUrls

  // Log the final combined array before returning
  logger.info(`[gnews.js] Total articles collected after specific/combined queries: ${allArticles.length}`); // Use logger

  // --- Topic-Only Fallback ---
  // If the specific and combined queries yield no results, try with just the topic.
  // Topic-only query is less likely to be too long, but we clean and truncate just in case.
  if (allArticles.length === 0 && topic) {
    let topicQuery = cleanAndTruncateQuery(topic); // Clean and truncate topic
    logger.info(`Specific/combined queries failed, attempting GNews fetch with topic only: ${topicQuery}`); // Use logger
    const topicOnlyArticles = await fetchArticles(topicQuery, seenUrls);
    logger.info(`[gnews.js] Fetched ${topicOnlyArticles.length} topic-only articles.`); // Use logger
    allArticles.push(...topicOnlyArticles); // Add topic-only articles, duplicates handled by seenUrls
    logger.info(`[gnews.js] Total articles collected after topic-only fallback: ${allArticles.length}`); // Use logger
  }

  // --- NewsAPI Fallback ---
  // If GNews (including topic-only) yielded no results, try NewsAPI.org
  if (allArticles.length === 0) {
    logger.info("[gnews.js] GNews yielded no results. Attempting NewsAPI.org fallback...");
    try {
      const fallbackArticles = await fetchNewsApiArticles(topic, keywords, seenUrls); // Pass seenUrls
      logger.info(`[gnews.js] NewsAPI fallback fetched ${fallbackArticles.length} articles.`);
      allArticles.push(...fallbackArticles); // Add fallback articles
      logger.info(`[gnews.js] Total articles collected after NewsAPI fallback: ${allArticles.length}`);
    } catch (fallbackError) {
      // Log the fallback error but don't necessarily stop; the final check will handle it.
      logger.error(`[gnews.js] Error during NewsAPI fallback: ${fallbackError.message}`);
    }
  }

  // Final check if any articles were found after all attempts (including fallback)
  if (allArticles.length === 0) {
    // Updated error message to reflect the addition of NewsAPI fallback attempt
    throw new Error("No articles found for the given topic/keywords, even after trying GNews (topic-only) and NewsAPI fallback. Please try different terms or check API limits.");
  }

  // console.log("[gnews.js] Final articles array:", allArticles); // Optional: Uncomment to log the full array if needed
  return allArticles;
};

// Helper function to fetch articles for a given query, handling pagination and retries
async function fetchArticles(query, seenUrls) {
  const fetchedArticles = [];
  const maxRetries = 3;
  const initialDelay = 5000; // Start with 5 seconds delay

  for (let page = 1; page <= 2; page++) { // Fetch up to 2 pages
    let retryCount = 0;
    let success = false;

    while (retryCount < maxRetries && !success) {
      const url = `https://gnews.io/api/v4/search?q=${encodeURIComponent(query)}&lang=en&max=30&page=${page}&token=${GNEWS_API_KEY}`;
      logger.info(`Fetching GNews page ${page}, attempt ${retryCount + 1}: ${url}`); // Use logger

      try {
        const response = await axios.get(url);

        // Check for non-200 status codes that Axios might not throw as errors
        if (response.status !== 200 || !response.data || !response.data.articles) {
          const errorMessage = response.data?.errors?.join(', ') || `HTTP status ${response.status}`;
          throw new Error(`GNews API Error on page ${page}: ${errorMessage}`);
        }

        const articlesFromPage = response.data.articles.map((article) => ({
          id: article.url, // Use URL as unique ID
          title: article.title,
          description: article.description || 'No description available.',
          source: article.source?.name || 'Unknown Source',
          imageUrl: article.image || null,
          url: article.url,
        }));

        for (const article of articlesFromPage) {
          // --- Added Detailed Logging for De-duplication ---
          logger.debug(`[gnews.js fetchArticles] Checking URL: ${article.url}`); // Use logger (debug level)
          if (!seenUrls.has(article.url)) {
            logger.debug(`[gnews.js fetchArticles] Adding new article: ${article.title} (${article.url})`); // Use logger (debug level)
            fetchedArticles.push(article);
            seenUrls.add(article.url);
          } else {
            logger.debug(`[gnews.js fetchArticles] Skipping duplicate article: ${article.title} (${article.url})`); // Use logger (debug level)
          }
          // --- End Detailed Logging ---
        }
        success = true; // Mark as success to move to the next page or finish
        logger.info(`Successfully fetched page ${page} for query "${query}"`); // Use logger

      } catch (error) {
        retryCount++;
        let errorMessage = `Error fetching GNews page ${page}: ${error.message}`;

        // Check if the error is due to rate limiting (429)
        if (error.response && error.response.status === 429) {
          const delay = Math.pow(2, retryCount -1) * initialDelay; // Exponential backoff
          errorMessage = `Rate limited on page ${page}. Retrying in ${delay}ms...`;
          logger.warn(errorMessage); // Use logger
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
           // Log other errors and break the retry loop for this page
           logger.error(errorMessage); // Use logger
           break; // Stop retrying for this page on non-429 errors
        }

        if (retryCount >= maxRetries) {
          logger.error(`Max retries reached for page ${page}. Skipping page.`); // Use logger
          // Stop trying for this page, but continue to the next page if applicable
          break;
        }
      }
    } // End of retry while loop
  } // End of page for loop

  return fetchedArticles; // Return articles fetched by this call
}

export default fetchTrendingArticles;

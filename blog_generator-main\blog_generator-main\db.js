import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
import { logger } from './logger.js'; // Import logger from the new module

dotenv.config();

const DB_HOST = process.env.DB_HOST;
const DB_USER = process.env.DB_USER;
const DB_PASSWORD = process.env.DB_PASSWORD;
const DB_NAME = process.env.DB_NAME;
const DB_PORT = process.env.DB_PORT || 3306; // Default MySQL port

if (!DB_HOST || !DB_USER || !DB_PASSWORD || !DB_NAME) {
  logger.error("Error: Missing required database environment variables."); // Use logger
  process.exit(1);
}

const dbPool = mysql.createPool({
  host: DB_HOST,
  user: DB_USER,
  password: DB_PASSWORD,
  database: DB_NAME,
  port: DB_PORT,
  waitForConnections: true,
  connectionLimit: 10, // Adjust as needed
  queueLimit: 0
});

// Test DB connection on startup (optional but recommended)
dbPool.getConnection()
  .then(connection => {
    logger.info('Successfully connected to the database.'); // Use logger
    connection.release();
  })
  .catch(err => {
    logger.error('Error connecting to the database:', err); // Use logger
    // Decide if you want to exit if DB connection fails
    process.exit(1); // Exit if DB connection fails on startup
  });

export default dbPool;

// Updated import for Google's generative AI
import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from '@google/generative-ai';
// Removed Vertex AI import
// Removed unused fs imports
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import dotenv from 'dotenv';
import { logger } from './logger.js'; // Import logger from the new module

dotenv.config();

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
// Removed GCP env vars
// Removed unused IMAGE_OUTPUT_DIR

if (!GEMINI_API_KEY) {
  logger.warn("Warning: Missing Gemini API key environment variable. AI functionality will be disabled."); // Use logger
  // Don't exit, just disable functionality
}

// Removed GCP env var check


// Initialize Gemini client (for text) only if API key is available
let genAI = null;
let textModel = null;

if (GEMINI_API_KEY) {
  genAI = new GoogleGenerativeAI(GEMINI_API_KEY);

  // Initialize the text model for blog generation
  textModel = genAI.getGenerativeModel({
    model: 'gemini-2.0-flash',
    safetySettings: [
      {
        category: HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
      },
      {
        category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
      }
    ],
  });
}

// Removed unused image generation models (promptGenModel, imageGenModel)
// Removed unused generateImageForBlogFromContent function


// --- Blog Text Generation ---

const generateBlogOptionsPrompt = (topic, keywords, selectedArticles, length, targetAudience, tone, userPreferences = null, sessionFeedbackText = null) => {
  let feedbackInstructions = "";

  // Prioritize session-specific feedback if provided
  if (sessionFeedbackText) {
    feedbackInstructions = `\n\nPlease refine the blog options based on the following specific feedback for this session:\n"${sessionFeedbackText}"\nIncorporate this feedback while generating the new set of options.`;
  }
  // Otherwise, use historical preferences if available
  else if (userPreferences && (userPreferences.liked?.length > 0 || userPreferences.disliked?.length > 0 || userPreferences.comments?.length > 0)) {
    feedbackInstructions = "\n\nPlease consider the user's past feedback to tailor the style and content:\n";
    const maxExamples = 5; // Limit examples
    if (userPreferences.liked?.length > 0) {
      feedbackInstructions += `- Liked examples (titles/summaries): ${userPreferences.liked.slice(-maxExamples).map(item => `"${item.title} - ${item.summary}"`).join(", ")}\n`;
    }
    if (userPreferences.disliked?.length > 0) {
      feedbackInstructions += `- Disliked examples (titles/summaries): ${userPreferences.disliked.slice(-maxExamples).map(item => `"${item.title} - ${item.summary}"`).join(", ")}\n`;
    }
    if (userPreferences.comments?.length > 0) {
      feedbackInstructions += `- Recent user comments: ${userPreferences.comments.slice(-maxExamples).join("; ")}\n`;
    }
    feedbackInstructions += "Adjust the tone, style, depth, and content focus accordingly to better match the user's preferences shown in the liked examples and avoid characteristics of the disliked examples/comments.";
  }

  // Enforce length, target audience, and tone
  const enforcedInstructions = `\n\nENSURE each blog draft strictly reflects the specified length (approximately ${length}), target audience (${targetAudience}), and tone (${tone}).`;

  // --- Conditional Prompt Generation ---
  let articleInstructions = "";
  let contentSourceInstructions = "";

  if (selectedArticles && selectedArticles.length > 0) {
    // Instructions when articles ARE provided
    articleInstructions = `
Use the following recent articles as primary sources for facts and insights:
${selectedArticles
    .map((a, i) => `Article ${i + 1}:\nTitle: ${a.title}\nDescription: ${a.description}\nURL: ${a.url}`)
    .join("\n\n")}
`;
    contentSourceInstructions = `
- **Strictly adhere** to the provided Topic, Keywords, and Selected Articles. **Do not** introduce information outside of this context.
- Each draft must be informative, original, and **directly synthesize** information **only** from the provided articles relevant to the topic and keywords.
- **IMPORTANT:** Do **not** explicitly mention the source articles in the blog content (e.g., avoid phrases like "Article 1 states...", "According to the selected article...", etc.). The blog should read as a standalone piece.
- Write each draft with a clear structure:
    - Introduction (clearly stating the blog's focus based on the topic/keywords)
    - 2-3 sections using markdown subheadings (## Subheading), developing points derived *solely* from the articles.
    - Conclusion`;
  } else {
    // Instructions when NO articles are provided
    logger.warn("Generating blog prompt without selected articles. Relying on topic/keywords and general knowledge."); // Add logging
    articleInstructions = `
No specific articles were provided or found. Generate the blog post based on the topic and keywords using your general knowledge.
`;
    contentSourceInstructions = `
- **Strictly adhere** to the provided Topic and Keywords.
- Each draft must be informative and original, drawing on general knowledge relevant to the topic and keywords.
- Write each draft with a clear structure:
    - Introduction (clearly stating the blog's focus based on the topic/keywords)
    - 2-3 sections using markdown subheadings (## Subheading), developing points relevant to the topic.
    - Conclusion`;
  }
  // --- End Conditional Prompt Generation ---


  return `
You are a professional content writer specializing in synthesizing information into engaging blog posts.
Your task is to create exactly 1 distinct, well-structured blog post draft with a concise summary, adhering to the specified constraints and user feedback if provided.

Topic: ${topic}
Keywords: ${keywords.join(", ")}
Desired Length: ${length}
Target Audience: ${targetAudience}
Desired Tone: ${tone}
${feedbackInstructions}
${enforcedInstructions}

${articleInstructions}

Instructions:
- Create 1 unique blog draft.
${contentSourceInstructions}
- Each draft must have:
    - "title": 5-10 word catchy blog title
    - "summary": concise 10-15 word summary accurately reflecting the draft's content.
    - "title": 5-10 word catchy blog title
    - "summary": concise 10-15 word summary accurately reflecting the draft's content.
    - "content": markdown content (as described above). The content should be well-researched, in-depth, and provide valuable insights for experts in the field.
**CRITICAL RESPONSE FORMATTING:**
Your entire response MUST be ONLY the generated text, formatted exactly as follows, with NO other text before or after:
TITLE: [Generated Title Here]
---END_TITLE---
SUMMARY: [Generated Summary Here]
---END_SUMMARY---
CONTENT:
[Generated Markdown Content Here]
---END_CONTENT---

Example:
TITLE: The Future of AI in Content Creation
---END_TITLE---
SUMMARY: Exploring how AI is revolutionizing content generation and its potential impacts.
---END_SUMMARY---
CONTENT:
## Introduction
AI is changing everything...
## Key Developments
...
## Conclusion
The future is bright...
---END_CONTENT---

Ensure each blog draft strictly reflects the specified topic, keywords, articles, length, target audience, and tone. **Irrelevant content is unacceptable.** If preference instructions were provided, make sure the generated blogs align with them while staying strictly relevant to the core request.
`;
};

// Added sessionFeedbackText parameter
const generateBlogOptions = async (topic, keywords, selectedArticles, length, targetAudience, tone, userId = null, dbPool, sessionFeedbackText = null) => {
  // Return empty array if no API key
  if (!GEMINI_API_KEY) {
    logger.warn("Gemini API key not available, returning empty blog options");
    return [];
  }

  let userPrefs = null; // Initialize as null

  // --- TEMPORARILY DISABLED PREFERENCE FETCHING ---
  logger.info("Temporarily skipping user preference fetching for debugging.");
  /*
  // Fetch historical preferences ONLY if userId is provided AND no sessionFeedbackText is given
  if (userId && !sessionFeedbackText) {
    logger.info(`Fetching historical preferences for userId ${userId} from database.`); // Use logger
    let connection; // Define connection here for broader scope within this block
    try {
      connection = await dbPool.getConnection();
      // Fetch recent feedback (adjust limits as needed)
      const feedbackLimit = 5; // Max examples for prompt
      const commentLimit = 5;

      // Fetch liked examples (title, summary) - Requires join with GeneratedBlogs
      // NOTE: This query might need adjustment if UserFeedback no longer reliably links to GeneratedBlogs via blog_id
      // For now, assuming the feedback table still holds relevant context (blog_title, blog_summary)
      const [likedRows] = await connection.query(
        `SELECT blog_title as title, blog_summary as summary
         FROM UserFeedback_marketing
         WHERE user_id = ? AND is_liked = TRUE
         ORDER BY feedback_at DESC
         LIMIT ?`,
        [userId, feedbackLimit]
      );

      // Fetch disliked examples (title, summary)
      const [dislikedRows] = await connection.query(
        `SELECT blog_title as title, blog_summary as summary
         FROM UserFeedback_marketing
         WHERE user_id = ? AND is_liked = FALSE
         ORDER BY feedback_at DESC
         LIMIT ?`,
        [userId, feedbackLimit]
      );

      // Fetch recent comments
      const [commentRows] = await connection.query(
        `SELECT comment
         FROM UserFeedback_marketing
         WHERE user_id = ? AND comment IS NOT NULL AND comment != ''
         ORDER BY feedback_at DESC
         LIMIT ?`,
        [userId, commentLimit]
      );

      // Structure preferences for the prompt generator
      userPrefs = {
        liked: likedRows.map(row => ({ title: row.title, summary: row.summary })),
        disliked: dislikedRows.map(row => ({ title: row.title, summary: row.summary })),
        comments: commentRows.map(row => row.comment)
      };
      logger.info(`Fetched historical preferences for userId ${userId}:`, JSON.stringify(userPrefs)); // Use logger

    } catch (dbError) {
      logger.error(`Database error fetching historical preferences for userId ${userId}:`, dbError); // Use logger
      userPrefs = null; // Proceed without preferences on error
    } finally {
      if (connection) connection.release(); // Ensure connection is released
    }
  } else if (sessionFeedbackText) {
      logger.info("Using session-specific feedback, skipping historical preference fetch."); // Use logger
  } else {
      logger.info("No userId provided and no session feedback; generating without user preferences."); // Use logger
  }
  */
  // --- END TEMPORARY DISABLE ---

  // Pass null for user preferences
  const prompt = generateBlogOptionsPrompt(topic, keywords, selectedArticles, length, targetAudience, tone, null, sessionFeedbackText); // Always pass null for userPrefs
  logger.debug("Generated Prompt for Gemini:", prompt);

  try {
    // Generate content with text model
    const result = await textModel.generateContent({
      contents: [{ parts: [{ text: prompt }] }]
    });
    
    const response = result.response;
    const rawText = response.text(); // Get the raw text
    // Log the raw response BEFORE parsing to see exactly what the model returned
    logger.info("Raw Gemini Response Text Received:", rawText);
    // logger.debug("Raw Gemini Response Text:", rawText); // Keep debug log as well if needed

    // --- Parse Delimited Text (More Flexible Regex) ---
    // Allow for potential variations in whitespace and ensure non-greedy matching.
    const titleMatch = rawText.match(/TITLE:\s*([\s\S]*?)\s*---END_TITLE---/);
    const summaryMatch = rawText.match(/SUMMARY:\s*([\s\S]*?)\s*---END_SUMMARY---/);
    // For content, capture everything after CONTENT: until the final delimiter, handling potential variations
    const contentMatch = rawText.match(/CONTENT:\s*([\s\S]*?)\s*---END_CONTENT---\s*$/); // Anchor to end of string with optional whitespace

    // Check if all parts were successfully matched
    if (!titleMatch?.[1] || !summaryMatch?.[1] || !contentMatch?.[1]) {
        logger.error("Could not parse title, summary, or content using delimiters from Gemini response.", {
            rawText,
            titleFound: !!titleMatch?.[1],
            summaryFound: !!summaryMatch?.[1],
            contentFound: !!contentMatch?.[1]
        });
        throw new Error("Failed to parse delimited text from AI model response. Check delimiters and format.");
    }

    const title = titleMatch[1].trim();
    const summary = summaryMatch[1].trim();
    // Trim potential leading/trailing newlines from content
    const content = contentMatch[1].trim();

    logger.info("Successfully parsed blog components using delimiters.");

    // Construct the result object in the expected format (array with one object)
    const parsedResult = [{ title, summary, content }];
    return parsedResult;
    // --- End Delimited Text Parsing ---

    // Removed the previous JSON parsing try/catch block
  } catch (err) {
      // Catch errors from the API call itself (before parsing)
      logger.error("Gemini API Call or Parsing Error in generateBlogOptions:", err); // Use logger
      // Re-throw the error instead of returning default data
      // The calling route (/generate-options) should handle this error.
      throw err;
      /* Original problematic code:
        return [
            { "title": "Default Title 1", "summary": "Default Summary 1", "content": "Default Content 1" },
            { "title": "Default Title 2", "summary": "Default Summary 2", "content": "Default Content 2" },
            { "title": "Default Title 3", "summary": "Default Summary 3", "content": "Default Content 3" }, // Corrected typo
            { "title": "Default Title 4", "summary": "Default Summary 4", "content": "Default Content 4" },
            { "title": "Default Title 5", "summary": "Default Summary 5", "content": "Default Content 5" }
        ];
      */
  }
};


// --- Keyword Generation ---
const generateKeywordsPrompt = (topic) => {
  return `
Generate a list of 10-15 relevant SEO keywords for the following blog topic.
Focus on terms that potential readers might use to search for content related to this topic.
Include a mix of short-tail and long-tail keywords.

Topic: ${topic}

Return ONLY a valid JSON array of strings. Do NOT include any text before or after the JSON array. Do NOT use markdown formatting (like \`\`\`json) around the JSON output.

Example:
["keyword research", "seo strategy", "long-tail keywords", "content marketing", "search engine optimization", "on-page seo", "technical seo", "link building", "google algorithm updates", "keyword difficulty"]
`;
};

const generateKeywords = async (topic) => {
  // Return empty array if no API key
  if (!GEMINI_API_KEY) {
    logger.warn("Gemini API key not available, returning empty keywords");
    return [];
  }

  const prompt = generateKeywordsPrompt(topic);
  logger.debug("Generated Prompt for Gemini (Keywords):", prompt); // Use logger

  try {
    const result = await textModel.generateContent({
      contents: [{ parts: [{ text: prompt }] }]
    });
    const response = result.response;
    const rawText = response.text();
    logger.debug("Raw Gemini Response Text (Keywords):", rawText); // Use logger

    // Clean the raw text
    let jsonString = rawText.replace(/^```json\s*|```$/g, '').trim();
    logger.debug("Cleaned JSON String (Keywords):", jsonString); // Use logger

    // Basic validation
    if (!jsonString.startsWith('[') || !jsonString.endsWith(']')) {
      logger.error("Cleaned text does not appear to be a valid JSON array (Keywords):", jsonString); // Use logger
      throw new Error("Received malformed JSON array structure from AI model for keywords.");
    }

    try {
      const parsedResult = JSON.parse(jsonString);
      if (!Array.isArray(parsedResult) || !parsedResult.every(item => typeof item === 'string')) {
        logger.error("Parsed result is not an array of strings (Keywords):", parsedResult); // Use logger
        throw new Error("Parsed JSON is not an array of strings.");
      }
      logger.info(`Successfully generated ${parsedResult.length} keywords for topic: ${topic}`); // Use logger
      return parsedResult;
    } catch (parseError) {
      logger.error("JSON Parsing Error Occurred (Keywords):", parseError.message); // Use logger
      logger.error("--- Raw Gemini Response Text (Keywords) ---"); // Use logger
      logger.error(rawText); // Use logger
      logger.error("--- Cleaned Text Attempted (Keywords) ---"); // Use logger
      logger.error(jsonString); // Use logger
      throw new Error(`Failed to parse keywords JSON from AI model. Parse Error: ${parseError.message}`);
    }
  } catch (err) {
    logger.error("Gemini API Call Error (Keywords):", err); // Use logger
    // Return empty array or throw error based on desired behavior
    // Throwing error might be better to signal failure upstream
    throw new Error(`Gemini API call failed during keyword generation: ${err.message}`);
  }
};


// --- Removed Image Generation Logic ---


// Export the functions
export {
  genAI,
  generateBlogOptions,
  // generateBlogOptionsPrompt, // Removed internal function from export
  generateKeywords // Add new function to exports
  // Removed generateImageForBlog export
};
